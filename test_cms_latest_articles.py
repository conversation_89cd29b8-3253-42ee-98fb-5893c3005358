#!/usr/bin/env python3
"""
测试CMS模块从MySQL数据库获取最新文章的功能
专门测试从localhost:3306获取最新文章数据
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'app'))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_cms_get_latest_articles():
    """测试CMS模块获取最新文章功能"""
    try:
        logger.info("=" * 60)
        logger.info("测试CMS模块从MySQL数据库获取最新文章")
        logger.info("=" * 60)
        
        # 1. 导入CMS模块
        logger.info("1. 导入CMS相关模块...")
        try:
            from backend.app.cms.database import get_db_session, Article, init_database
            from backend.app.cms.service import CMSService
            logger.info("✓ CMS模块导入成功")
        except ImportError as e:
            logger.error(f"✗ CMS模块导入失败: {e}")
            return False
        
        # 2. 初始化数据库连接
        logger.info("2. 初始化数据库连接...")
        db_success = init_database()
        if not db_success:
            logger.error("✗ 数据库初始化失败")
            return False
        logger.info("✓ 数据库初始化成功")
        
        # 3. 创建CMS服务实例
        logger.info("3. 创建CMS服务实例...")
        cms_service = CMSService()
        logger.info("✓ CMS服务实例创建成功")
        
        # 4. 获取数据库会话
        logger.info("4. 获取数据库会话...")
        db = get_db_session()
        if db is None:
            logger.error("✗ 无法获取数据库会话")
            return False
        logger.info("✓ 数据库会话获取成功")
        
        try:
            # 5. 测试获取所有文章
            logger.info("5. 测试获取所有文章...")
            articles, total = cms_service.get_articles(db, page=1, page_size=10)
            logger.info(f"✓ 获取到 {len(articles)} 篇文章，总数: {total}")
            
            if articles:
                logger.info("   最新文章列表:")
                for i, article in enumerate(articles, 1):
                    logger.info(f"   {i}. ID: {article.id}")
                    logger.info(f"      标题: {article.title}")
                    logger.info(f"      作者: {article.author}")
                    logger.info(f"      分类: {article.category}")
                    logger.info(f"      状态: {article.status}")
                    logger.info(f"      创建时间: {article.created_at}")
                    logger.info(f"      更新时间: {article.updated_at}")
                    logger.info(f"      来源URL: {article.source_url}")
                    logger.info(f"      内容预览: {(article.content or '')[:100]}...")
                    logger.info("")
            
            # 6. 测试获取已发布文章
            logger.info("6. 测试获取已发布文章...")
            published_articles, published_total = cms_service.get_articles(
                db, page=1, page_size=10, status="published"
            )
            logger.info(f"✓ 获取到 {len(published_articles)} 篇已发布文章，总数: {published_total}")
            
            # 7. 测试按时间排序获取最新文章
            logger.info("7. 测试获取最近24小时的文章...")
            recent_time = datetime.now() - timedelta(hours=24)
            
            # 直接查询数据库获取最新文章
            recent_articles = db.query(Article).filter(
                Article.is_deleted == False,
                Article.status == "published",
                Article.created_at >= recent_time
            ).order_by(Article.created_at.desc()).limit(5).all()
            
            logger.info(f"✓ 最近24小时内创建的文章: {len(recent_articles)} 篇")
            
            for article in recent_articles:
                logger.info(f"   - {article.title} (创建于: {article.created_at})")
            
            # 8. 测试获取最新更新的文章
            logger.info("8. 测试获取最新更新的文章...")
            latest_updated = db.query(Article).filter(
                Article.is_deleted == False,
                Article.status == "published"
            ).order_by(Article.updated_at.desc()).limit(3).all()
            
            logger.info(f"✓ 最新更新的文章: {len(latest_updated)} 篇")
            
            for article in latest_updated:
                logger.info(f"   - {article.title} (更新于: {article.updated_at})")
            
            # 9. 测试按分类获取文章
            logger.info("9. 测试按分类获取文章...")
            categories = db.query(Article.category).filter(
                Article.is_deleted == False,
                Article.category.isnot(None)
            ).distinct().all()
            
            logger.info(f"✓ 发现 {len(categories)} 个分类:")
            for category in categories:
                if category[0]:
                    category_articles, category_total = cms_service.get_articles(
                        db, page=1, page_size=5, category=category[0]
                    )
                    logger.info(f"   - {category[0]}: {category_total} 篇文章")
            
            # 10. 测试获取待添加到RAG的文章
            logger.info("10. 测试获取待添加到RAG的文章...")
            try:
                articles_to_add = cms_service.get_articles_to_add(db)
                logger.info(f"✓ 需要添加到RAG的文章: {len(articles_to_add)} 篇")
                
                for article in articles_to_add[:3]:  # 只显示前3个
                    logger.info(f"   - ID: {article.get('content_id')}, 标题: {article.get('title')}")
                    
            except Exception as e:
                logger.warning(f"获取待添加文章时出错（可能是RAG服务未启动）: {e}")
            
            logger.info("=" * 60)
            logger.info("CMS模块测试完成 - 所有功能正常")
            logger.info("=" * 60)
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"CMS模块测试失败: {e}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return False

def test_direct_mysql_query():
    """直接测试MySQL数据库查询"""
    try:
        logger.info("\n" + "=" * 60)
        logger.info("直接测试MySQL数据库查询最新文章")
        logger.info("=" * 60)
        
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        
        # 数据库配置
        db_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '5Secsgo100',
            'database': 'chestnut_cms',
            'charset': 'utf8mb4'
        }
        
        # 创建数据库连接
        database_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        engine = create_engine(database_url, echo=False)
        
        with engine.connect() as connection:
            # 查询最新的5篇文章
            logger.info("1. 查询最新的5篇已发布文章...")
            result = connection.execute(text("""
                SELECT id, title, author, category, status, created_at, updated_at, source_url
                FROM articles 
                WHERE is_deleted = 0 AND status = 'published'
                ORDER BY created_at DESC 
                LIMIT 5
            """))
            
            latest_articles = result.fetchall()
            logger.info(f"✓ 找到 {len(latest_articles)} 篇最新文章:")
            
            for article in latest_articles:
                logger.info(f"   ID: {article[0]}")
                logger.info(f"   标题: {article[1]}")
                logger.info(f"   作者: {article[2]}")
                logger.info(f"   分类: {article[3]}")
                logger.info(f"   状态: {article[4]}")
                logger.info(f"   创建时间: {article[5]}")
                logger.info(f"   更新时间: {article[6]}")
                logger.info(f"   来源URL: {article[7]}")
                logger.info("")
            
            # 查询各分类的文章数量
            logger.info("2. 查询各分类的文章数量...")
            result = connection.execute(text("""
                SELECT category, COUNT(*) as count
                FROM articles 
                WHERE is_deleted = 0 AND status = 'published' AND category IS NOT NULL
                GROUP BY category
                ORDER BY count DESC
            """))
            
            category_stats = result.fetchall()
            logger.info(f"✓ 分类统计:")
            for category, count in category_stats:
                logger.info(f"   {category}: {count} 篇")
            
            # 查询最近更新的文章
            logger.info("3. 查询最近更新的文章...")
            result = connection.execute(text("""
                SELECT id, title, updated_at
                FROM articles 
                WHERE is_deleted = 0 AND status = 'published'
                ORDER BY updated_at DESC 
                LIMIT 3
            """))
            
            recent_updated = result.fetchall()
            logger.info(f"✓ 最近更新的文章:")
            for article in recent_updated:
                logger.info(f"   {article[1]} (更新于: {article[2]})")
        
        logger.info("=" * 60)
        logger.info("直接MySQL查询测试完成")
        logger.info("=" * 60)
        return True
        
    except Exception as e:
        logger.error(f"直接MySQL查询测试失败: {e}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("开始测试CMS模块获取最新文章功能")
    logger.info(f"测试时间: {datetime.now()}")
    
    # 测试直接MySQL查询
    mysql_success = test_direct_mysql_query()
    
    # 测试CMS模块功能
    cms_success = test_cms_get_latest_articles()
    
    if mysql_success and cms_success:
        logger.info("\n🎉 所有测试通过！CMS模块可以正常从MySQL数据库获取最新文章。")
    else:
        logger.error("\n❌ 部分测试失败")

if __name__ == "__main__":
    main()
