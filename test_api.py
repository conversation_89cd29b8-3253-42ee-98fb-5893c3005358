#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

try:
    # 测试 articles/to-add API
    print('=== 测试 articles/to-add API ===')
    response = requests.get('http://localhost:9000/articles/to-add', timeout=30)
    print(f'状态码: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'返回的待添加文章数量: {len(data)}')
        if len(data) > 0:
            print('前3篇文章示例:')
            for i, article in enumerate(data[:3]):
                title = article.get('title', '')[:50]
                print(f'  {i+1}. ID: {article.get("content_id")}, 标题: {title}...')
        else:
            print('没有待添加的文章')
    else:
        print(f'API调用失败: {response.text}')
        
    print()
    
    # 测试 RAG documents API
    print('=== 测试 RAG documents API ===')
    response = requests.get('http://localhost:9000/api/documents', timeout=30)
    print(f'状态码: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            documents = data.get('documents', [])
            print(f'RAG中的文档数量: {len(documents)}')
            if len(documents) > 0:
                print('RAG中的文档:')
                for i, doc in enumerate(documents):
                    filename = doc.get('filename', '')
                    chunks = doc.get('chunks_count', 0)
                    print(f'  {i+1}. {filename}, 块数: {chunks}')
        else:
            print(f'RAG API返回错误: {data.get("message")}')
    else:
        print(f'RAG API调用失败: {response.text}')
        
except Exception as e:
    print(f'测试过程中出错: {e}')
